#!/usr/bin/env python3
"""
🏗️ HORUS - محلل هيكل المشروع
Structure Analyzer for HORUS System
"""

import os
from pathlib import Path
from collections import defaultdict

class StructureAnalyzer:
    """محلل هيكل المشروع"""
    
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        self.project_name = self.project_path.name
        
        # أنماط اكتشاف أنواع المشاريع
        self.project_indicators = {
            "Python": {
                "files": ["requirements.txt", "setup.py", "pyproject.toml", "Pipfile"],
                "extensions": [".py"],
                "directories": ["venv", "env", "__pycache__"]
            },
            "Node.js": {
                "files": ["package.json", "package-lock.json", "yarn.lock"],
                "extensions": [".js", ".ts", ".jsx", ".tsx"],
                "directories": ["node_modules", "dist", "build"]
            },
            "Java": {
                "files": ["pom.xml", "build.gradle", "gradlew"],
                "extensions": [".java", ".class", ".jar"],
                "directories": ["src/main/java", "target", "build"]
            },
            "C#": {
                "files": [".csproj", ".sln", "packages.config"],
                "extensions": [".cs", ".dll", ".exe"],
                "directories": ["bin", "obj", "packages"]
            },
            "PHP": {
                "files": ["composer.json", "composer.lock"],
                "extensions": [".php"],
                "directories": ["vendor", "public"]
            },
            "Ruby": {
                "files": ["Gemfile", "Gemfile.lock", "Rakefile"],
                "extensions": [".rb"],
                "directories": ["gems", "vendor/bundle"]
            },
            "Go": {
                "files": ["go.mod", "go.sum"],
                "extensions": [".go"],
                "directories": ["vendor"]
            },
            "Rust": {
                "files": ["Cargo.toml", "Cargo.lock"],
                "extensions": [".rs"],
                "directories": ["target", "src"]
            },
            "Docker": {
                "files": ["Dockerfile", "docker-compose.yml", "docker-compose.yaml", ".dockerignore"],
                "extensions": [],
                "directories": []
            },
            "Web": {
                "files": ["index.html", "index.htm"],
                "extensions": [".html", ".htm", ".css"],
                "directories": ["css", "js", "assets", "static"]
            },
            "React": {
                "files": ["package.json"],
                "extensions": [".jsx", ".tsx"],
                "directories": ["src", "public", "build"]
            },
            "Vue": {
                "files": ["vue.config.js", "nuxt.config.js"],
                "extensions": [".vue"],
                "directories": ["src", "components", "pages"]
            },
            "Angular": {
                "files": ["angular.json", "ng-package.json"],
                "extensions": [".component.ts", ".service.ts"],
                "directories": ["src/app", "dist"]
            }
        }
    
    def analyze(self):
        """تحليل هيكل المشروع"""
        results = {
            "project_name": self.project_name,
            "project_path": str(self.project_path),
            "project_types": self.detect_project_types(),
            "file_statistics": self.analyze_files(),
            "directory_structure": self.analyze_directories(),
            "large_files": self.find_large_files(),
            "file_type_distribution": self.analyze_file_types(),
            "complexity_metrics": self.calculate_complexity()
        }
        
        return results
    
    def detect_project_types(self):
        """اكتشاف أنواع المشروع"""
        detected_types = []
        
        for project_type, indicators in self.project_indicators.items():
            score = 0
            
            # فحص الملفات المميزة
            for file_indicator in indicators["files"]:
                if self.file_exists_in_project(file_indicator):
                    score += 3
            
            # فحص الامتدادات
            for ext in indicators["extensions"]:
                if self.has_files_with_extension(ext):
                    score += 2
            
            # فحص المجلدات المميزة
            for dir_indicator in indicators["directories"]:
                if self.directory_exists_in_project(dir_indicator):
                    score += 1
            
            # إضافة النوع إذا كان النتيجة كافية
            if score >= 2:
                detected_types.append({
                    "type": project_type,
                    "confidence": min(score * 10, 100),
                    "indicators_found": score
                })
        
        # ترتيب حسب الثقة
        detected_types.sort(key=lambda x: x["confidence"], reverse=True)
        
        return detected_types
    
    def analyze_files(self):
        """تحليل إحصائيات الملفات"""
        total_files = 0
        total_size = 0
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                total_files += 1
                try:
                    total_size += file_path.stat().st_size
                except (OSError, PermissionError):
                    pass
        
        return {
            "total_files": total_files,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "average_file_size_kb": round((total_size / total_files) / 1024, 2) if total_files > 0 else 0
        }
    
    def analyze_directories(self):
        """تحليل هيكل المجلدات"""
        total_dirs = 0
        max_depth = 0
        dir_structure = {}
        
        for dir_path in self.project_path.rglob("*"):
            if dir_path.is_dir():
                total_dirs += 1
                
                # حساب العمق
                relative_path = dir_path.relative_to(self.project_path)
                depth = len(relative_path.parts)
                max_depth = max(max_depth, depth)
                
                # بناء هيكل المجلدات (المستوى الأول فقط)
                if depth == 1:
                    dir_name = relative_path.name
                    file_count = sum(1 for f in dir_path.rglob("*") if f.is_file())
                    dir_structure[dir_name] = {
                        "file_count": file_count,
                        "subdirectories": sum(1 for d in dir_path.iterdir() if d.is_dir())
                    }
        
        return {
            "total_directories": total_dirs,
            "max_depth": max_depth,
            "root_directories": dir_structure,
            "directory_count_by_level": self.count_dirs_by_level()
        }
    
    def find_large_files(self, size_threshold_mb=1):
        """العثور على الملفات الكبيرة"""
        large_files = []
        threshold_bytes = size_threshold_mb * 1024 * 1024
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                try:
                    size = file_path.stat().st_size
                    if size > threshold_bytes:
                        relative_path = file_path.relative_to(self.project_path)
                        large_files.append({
                            "path": str(relative_path),
                            "size_mb": round(size / (1024 * 1024), 2),
                            "extension": file_path.suffix.lower()
                        })
                except (OSError, PermissionError):
                    pass
        
        # ترتيب حسب الحجم
        large_files.sort(key=lambda x: x["size_mb"], reverse=True)
        
        return large_files[:20]  # أكبر 20 ملف
    
    def analyze_file_types(self):
        """تحليل توزيع أنواع الملفات"""
        file_types = defaultdict(int)
        extension_sizes = defaultdict(int)
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                extension = file_path.suffix.lower() or "no_extension"
                file_types[extension] += 1
                
                try:
                    size = file_path.stat().st_size
                    extension_sizes[extension] += size
                except (OSError, PermissionError):
                    pass
        
        # تحويل إلى قائمة مرتبة
        sorted_types = sorted(file_types.items(), key=lambda x: x[1], reverse=True)
        
        return {
            "file_count_by_extension": dict(sorted_types[:20]),  # أكثر 20 نوع
            "size_by_extension_mb": {
                ext: round(size / (1024 * 1024), 2) 
                for ext, size in extension_sizes.items()
            },
            "most_common_extension": sorted_types[0][0] if sorted_types else None,
            "total_extensions": len(file_types)
        }
    
    def calculate_complexity(self):
        """حساب مقاييس التعقيد"""
        file_stats = self.analyze_files()
        dir_stats = self.analyze_directories()
        
        # مقياس التعقيد البسيط
        complexity_score = 0
        
        # التعقيد بناءً على عدد الملفات
        if file_stats["total_files"] > 1000:
            complexity_score += 3
        elif file_stats["total_files"] > 100:
            complexity_score += 2
        elif file_stats["total_files"] > 10:
            complexity_score += 1
        
        # التعقيد بناءً على عمق المجلدات
        if dir_stats["max_depth"] > 8:
            complexity_score += 3
        elif dir_stats["max_depth"] > 5:
            complexity_score += 2
        elif dir_stats["max_depth"] > 3:
            complexity_score += 1
        
        # التعقيد بناءً على حجم المشروع
        if file_stats["total_size_mb"] > 1000:
            complexity_score += 3
        elif file_stats["total_size_mb"] > 100:
            complexity_score += 2
        elif file_stats["total_size_mb"] > 10:
            complexity_score += 1
        
        complexity_levels = {
            0: "بسيط جداً",
            1: "بسيط", 
            2: "بسيط",
            3: "متوسط",
            4: "متوسط",
            5: "معقد",
            6: "معقد",
            7: "معقد جداً",
            8: "معقد جداً",
            9: "معقد جداً"
        }
        
        return {
            "complexity_score": complexity_score,
            "complexity_level": complexity_levels.get(complexity_score, "معقد جداً"),
            "factors": {
                "file_count": file_stats["total_files"],
                "directory_depth": dir_stats["max_depth"],
                "project_size_mb": file_stats["total_size_mb"]
            }
        }
    
    def file_exists_in_project(self, filename):
        """فحص وجود ملف في المشروع"""
        return (self.project_path / filename).exists()
    
    def directory_exists_in_project(self, dirname):
        """فحص وجود مجلد في المشروع"""
        # فحص المسار المباشر
        if (self.project_path / dirname).exists():
            return True
        
        # فحص في أي مكان في المشروع
        for path in self.project_path.rglob(dirname):
            if path.is_dir():
                return True
        
        return False
    
    def has_files_with_extension(self, extension):
        """فحص وجود ملفات بامتداد معين"""
        pattern = f"*{extension}"
        return any(self.project_path.rglob(pattern))
    
    def count_dirs_by_level(self):
        """عد المجلدات حسب المستوى"""
        level_counts = defaultdict(int)
        
        for dir_path in self.project_path.rglob("*"):
            if dir_path.is_dir():
                relative_path = dir_path.relative_to(self.project_path)
                level = len(relative_path.parts)
                level_counts[level] += 1
        
        return dict(level_counts)
