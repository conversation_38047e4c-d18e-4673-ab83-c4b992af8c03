{"metadata": {"analysis_id": "ab6ab30a-8ee6-4c04-9bcd-cf63b79d0a86", "timestamp": "2025-07-07T11:09:40.995786", "analyzer_version": "HORUS 1.0.0", "project_name": "", "project_path": "."}, "structure": {"project_name": "", "project_path": ".", "project_types": [{"type": "Python", "confidence": 30, "indicators_found": 3}], "file_statistics": {"total_files": 18, "total_size_bytes": 174767, "total_size_mb": 0.17, "average_file_size_kb": 9.48}, "directory_structure": {"total_directories": 8, "max_depth": 2, "root_directories": {"ANALYZERS": {"file_count": 7, "subdirectories": 1}, "CONFIG": {"file_count": 1, "subdirectories": 0}, "DOCS": {"file_count": 1, "subdirectories": 0}, "INTERFACES": {"file_count": 1, "subdirectories": 0}, "MEMORY": {"file_count": 5, "subdirectories": 1}, "TOOLS": {"file_count": 1, "subdirectories": 0}}, "directory_count_by_level": {"1": 6, "2": 2}}, "large_files": [], "file_type_distribution": {"file_count_by_extension": {".py": 7, ".json": 4, ".pyc": 4, ".md": 2, ".bat": 1}, "size_by_extension_mb": {".md": 0.02, ".bat": 0.0, ".py": 0.09, ".json": 0.0, ".pyc": 0.06}, "most_common_extension": ".py", "total_extensions": 5}, "complexity_metrics": {"complexity_score": 1, "complexity_level": "بسيط", "factors": {"file_count": 18, "directory_depth": 2, "project_size_mb": 0.17}}}, "dependencies": {"python_dependencies": {"requirements_txt": [], "setup_py": [], "pyproject_toml": [], "pipfile": [], "conda_env": [], "imports": [{"module": "pathlib", "count": 7}, {"module": "json", "count": 4}, {"module": "collections", "count": 4}, {"module": "os", "count": 3}, {"module": "datetime", "count": 3}, {"module": "re", "count": 2}, {"module": "sys", "count": 2}, {"module": "uuid", "count": 2}]}, "nodejs_dependencies": {"package_json": {}, "yarn_lock": [], "package_lock": []}, "java_dependencies": {"maven_pom": [], "gradle_build": [], "ivy_xml": []}, "docker_dependencies": {"dockerfile": [], "docker_compose": [], "base_images": [], "exposed_ports": []}, "other_dependencies": {"composer_php": [], "gemfile_ruby": [], "cargo_rust": [], "go_mod": []}, "dependency_summary": {"total_dependencies": 0, "by_language": {}, "critical_dependencies": [], "outdated_patterns": [], "security_concerns": []}}, "quality": {"file_categories": {"counts": {"documentation_files": 2, "test_files": 0, "config_files": 4, "code_files": 7, "asset_files": 0, "other_files": 5}, "details": {"documentation": ["README.md", "DOCS\\HORUS-USAGE-GUIDE.md"], "tests": [], "config": ["CONFIG\\horus_config.json", "MEMORY\\analysis_history.json", "MEMORY\\learning_data.json", "MEMORY\\projects_database.json"], "code": ["ANALYZERS\\dependency_analyzer.py", "ANALYZERS\\project_analyzer.py", "ANALYZERS\\quality_analyzer.py", "ANALYZERS\\structure_analyzer.py", "INTERFACES\\main_interface.py", "MEMORY\\memory_manager.py", "TOOLS\\report_viewer.py"], "assets": [], "other": ["START-HORUS.bat", "MEMORY\\__pycache__\\memory_manager.cpython-313.pyc", "ANALYZERS\\__pycache__\\dependency_analyzer.cpython-313.pyc", "ANALYZERS\\__pycache__\\quality_analyzer.cpython-313.pyc", "ANALYZERS\\__pycache__\\structure_analyzer.cpython-313.pyc"]}}, "documentation_analysis": {"has_readme": true, "has_changelog": true, "has_license": false, "has_contributing": false, "documentation_score": 5, "documentation_files": ["README.md", "DOCS\\HORUS-USAGE-GUIDE.md"], "documentation_quality": 62.5}, "test_coverage_estimate": {"test_files": 0, "code_files": 7, "test_lines": 0, "code_lines": 2266, "test_to_code_file_ratio": 0.0, "test_to_code_lines_ratio": 0.0, "estimated_coverage": 0.0}, "code_organization": {"directory_depth": 2, "files_per_directory": {"root": 2, "ANALYZERS": 4, "CONFIG": 1, "DOCS": 1, "INTERFACES": 1, "MEMORY": 4, "TOOLS": 1, "MEMORY\\__pycache__": 1, "ANALYZERS\\__pycache__": 3}, "large_files": [], "organization_score": 10}, "naming_conventions": {"file_naming_patterns": {"camelCase": 1, "kebab_case": 2, "snake_case": 15}, "directory_naming_patterns": {"camelCase": 6, "snake_case": 2}, "consistency_score": 79.16666666666667}, "complexity_indicators": {"average_file_size": 323.7142857142857, "max_file_size": 433, "deeply_nested_files": 0, "complexity_score": 9}, "quality_metrics": {"documentation_files": 2, "test_files": 0, "code_files": 7, "total_files": 18, "doc_ratio": "11.1%", "test_ratio": "0.0%", "doc_ratio_numeric": 11.1, "test_ratio_numeric": 0.0, "overall_quality_score": 56.5, "quality_grade": "يحتاج تحسين"}}, "recommendations": [{"type": "testing", "priority": "high", "title": "زيادة الاختبارات", "description": "نسبة الاختبارات منخفضة (0.0%)، يُنصح بإضافة المزيد"}, {"type": "documentation", "priority": "medium", "title": "تحسين التوثيق", "description": "نسبة التوثيق منخفضة (0.0%)، يُنصح بإضافة README وتوثيق"}, {"type": "learning", "priority": "high", "title": "زيادة الاختبارات بناءً على التجربة", "description": "المشاريع المشابهة التي تحتوي على اختبارات أكثر تحقق جودة أفضل"}, {"type": "learning", "priority": "medium", "title": "تحسين التوثيق بناءً على أفضل الممارسات", "description": "التوثيق الجيد يحسن من قابلية الصيانة والتطوير"}]}