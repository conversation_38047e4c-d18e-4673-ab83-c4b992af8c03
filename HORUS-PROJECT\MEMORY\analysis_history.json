{"history_metadata": {"version": "1.0.0", "created": "2025-07-07T10:00:00Z", "total_analyses": 1, "retention_days": 365}, "analyses": [{"analysis_id": "ab6ab30a-8ee6-4c04-9bcd-cf63b79d0a86", "timestamp": "2025-07-07T11:09:40.995786", "project_name": "", "project_types": [{"type": "Python", "confidence": 30, "indicators_found": 3}], "quality_score": 56.5, "recommendations_count": 4, "file_count": 18}], "trends": {"monthly_analysis_count": {}, "popular_project_types": {}, "improvement_trends": {}, "common_recommendations": {}}, "comparisons": {"before_after": [], "project_similarities": [], "performance_improvements": []}}