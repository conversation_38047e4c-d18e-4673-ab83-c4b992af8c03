#!/usr/bin/env python3
"""
🦅 HORUS - الواجهة الرئيسية (نظام منفصل)
Main Interface for HORUS Independent System
"""

import os
import sys
import json
from pathlib import Path

# إضافة مسار HORUS للاستيراد
sys.path.append(str(Path(__file__).parent.parent))

try:
    from ANALYZERS.project_analyzer import HorusProjectAnalyzer
except ImportError:
    print("❌ خطأ: لا يمكن استيراد محلل المشاريع")
    sys.exit(1)

class HorusMainInterface:
    """الواجهة الرئيسية لنظام HORUS المنفصل"""
    
    def __init__(self):
        self.horus_root = Path(__file__).parent.parent
        self.current_analyzer = None
        
        print(f"🦅 تم تهيئة نظام HORUS المنفصل")
        print(f"📍 موقع النظام: {self.horus_root}")
    
    def show_welcome(self):
        """عرض شاشة الترحيب"""
        print("🦅" + "=" * 58 + "🦅")
        print("🦅" + " " * 58 + "🦅")
        print("🦅" + "    HORUS - محلل المشاريع المتقدم    ".center(58) + "🦅")
        print("🦅" + "    نظام منفصل ومستقل    ".center(58) + "🦅")
        print("🦅" + "    Independent System    ".center(58) + "🦅")
        print("🦅" + " " * 58 + "🦅")
        print("🦅" + "=" * 58 + "🦅")
        print()
        print("🎯 الخيارات المتاحة:")
        print("1. 🔍 تحليل مشروع جديد")
        print("2. 📊 عرض آخر تحليل")
        print("3. 📋 عرض تاريخ التحليلات")
        print("4. 🧠 إحصائيات الذاكرة")
        print("5. 📄 عرض التقارير المحفوظة")
        print("6. 🔧 إعدادات النظام")
        print("7. ℹ️  معلومات حول HORUS")
        print("0. 🚪 خروج")
        print("🦅" + "=" * 58 + "🦅")
    
    def analyze_new_project(self):
        """تحليل مشروع جديد"""
        print("\n🔍 تحليل مشروع جديد")
        print("=" * 40)
        
        print("اختر نوع التحليل:")
        print("1. 📁 تحليل المشروع الحالي")
        print("2. 📂 تحليل مشروع آخر")
        print("0. 🔙 رجوع")
        
        choice = input("\n🎯 اختيارك: ").strip()
        
        if choice == "1":
            self.analyze_current_project()
        elif choice == "2":
            self.analyze_other_project()
        elif choice == "0":
            return
        else:
            print("❌ اختيار غير صحيح")
    
    def analyze_current_project(self):
        """تحليل المشروع الحالي"""
        current_dir = Path(".").resolve()
        print(f"\n🔍 تحليل المشروع الحالي")
        print(f"📁 اسم المشروع: {current_dir.name}")
        print(f"📍 المسار: {current_dir}")
        
        confirm = input("\n❓ هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ تم إلغاء التحليل")
            return
        
        print("\n🚀 بدء التحليل...")
        self.current_analyzer = HorusProjectAnalyzer(current_dir)
        results = self.current_analyzer.run_full_analysis()
        
        if results:
            print("\n✅ تم التحليل بنجاح!")
            self.show_analysis_summary(results)
        else:
            print("\n❌ فشل في التحليل")
    
    def analyze_other_project(self):
        """تحليل مشروع آخر"""
        print("\n📂 تحليل مشروع آخر")
        print("-" * 30)
        
        project_path = input("📁 أدخل مسار المشروع: ").strip()
        if not project_path:
            print("❌ لم تدخل مسار")
            return
        
        project_path = Path(project_path)
        if not project_path.exists():
            print(f"❌ المسار غير موجود: {project_path}")
            return
        
        if not project_path.is_dir():
            print(f"❌ المسار ليس مجلد: {project_path}")
            return
        
        print(f"\n🔍 تحليل المشروع: {project_path.name}")
        print(f"📍 المسار: {project_path}")
        
        confirm = input("\n❓ هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ تم إلغاء التحليل")
            return
        
        print("\n🚀 بدء التحليل...")
        self.current_analyzer = HorusProjectAnalyzer(project_path)
        results = self.current_analyzer.run_full_analysis()
        
        if results:
            print("\n✅ تم التحليل بنجاح!")
            self.show_analysis_summary(results)
        else:
            print("\n❌ فشل في التحليل")
    
    def show_last_analysis(self):
        """عرض آخر تحليل"""
        if not self.current_analyzer:
            print("❌ لا يوجد تحليل سابق في هذه الجلسة")
            return
        
        print("\n📊 آخر تحليل في الجلسة:")
        print("=" * 40)
        self.current_analyzer.display_summary()
    
    def show_analysis_history(self):
        """عرض تاريخ التحليلات"""
        print("\n📋 تاريخ التحليلات")
        print("=" * 40)
        
        memory_file = self.horus_root / "MEMORY" / "projects_database.json"
        
        try:
            if memory_file.exists():
                with open(memory_file, 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
                
                total_projects = memory_data.get("horus_memory", {}).get("total_projects_analyzed", 0)
                last_updated = memory_data.get("horus_memory", {}).get("last_updated", "غير محدد")
                
                print(f"📊 إجمالي المشاريع المحللة: {total_projects}")
                print(f"🔄 آخر تحديث: {last_updated[:16] if last_updated != 'غير محدد' else last_updated}")
                
                projects = memory_data.get("projects", {})
                if projects:
                    print(f"\n📁 المشاريع المحللة:")
                    for i, (key, project) in enumerate(list(projects.items())[-5:], 1):
                        name = project.get("name", "غير محدد")
                        last_analyzed = project.get("last_analyzed", "غير محدد")[:16]
                        quality_score = project.get("summary", {}).get("quality_score", 0)
                        print(f"  {i}. {name} - {last_analyzed} (جودة: {quality_score}/100)")
                else:
                    print("📭 لا توجد مشاريع محللة بعد")
            else:
                print("❌ ملف الذاكرة غير موجود")
        
        except Exception as e:
            print(f"❌ خطأ في قراءة التاريخ: {e}")
    
    def show_memory_statistics(self):
        """عرض إحصائيات الذاكرة"""
        print("\n🧠 إحصائيات ذاكرة HORUS (نظام منفصل)")
        print("=" * 50)
        
        memory_file = self.horus_root / "MEMORY" / "projects_database.json"
        
        try:
            if memory_file.exists():
                with open(memory_file, 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
                
                horus_memory = memory_data.get("horus_memory", {})
                system_info = memory_data.get("system_info", {})
                
                print(f"💾 حجم الذاكرة: {horus_memory.get('memory_size_mb', 0)} MB")
                print(f"📁 المشاريع المحفوظة: {horus_memory.get('total_projects_analyzed', 0)}")
                print(f"🏠 موقع النظام: {system_info.get('project_location', 'غير محدد')}")
                print(f"🛡️ حالة النظام: {horus_memory.get('status', 'INDEPENDENT_SYSTEM')}")
                print(f"🔄 آخر تحديث: {horus_memory.get('last_updated', 'غير محدد')[:16]}")
                print(f"📅 تاريخ الفصل: {system_info.get('separation_date', 'غير محدد')[:16]}")
                print(f"🧠 حالة التعلم: نشط")
                
                projects_count = horus_memory.get('total_projects_analyzed', 0)
                if projects_count > 0:
                    print(f"📈 متوسط التحليلات: {projects_count} مشروع")
            else:
                print("❌ ملف الذاكرة غير موجود")
        
        except Exception as e:
            print(f"❌ خطأ في قراءة الإحصائيات: {e}")
    
    def show_saved_reports(self):
        """عرض التقارير المحفوظة"""
        print("\n📄 التقارير المحفوظة")
        print("=" * 40)
        
        reports_dir = self.horus_root / "REPORTS" / "json"
        
        if not reports_dir.exists():
            print("❌ لا توجد تقارير محفوظة")
            return
        
        report_files = list(reports_dir.glob("horus_analysis_*.json"))
        
        if not report_files:
            print("❌ لا توجد تقارير محفوظة")
            return
        
        # ترتيب التقارير حسب التاريخ
        report_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        print(f"📊 تم العثور على {len(report_files)} تقرير:")
        
        for i, report_file in enumerate(report_files[:10], 1):
            # استخراج معلومات من اسم الملف
            name_parts = report_file.stem.split('_')
            if len(name_parts) >= 3:
                project_name = name_parts[2]
                timestamp = name_parts[-1] if len(name_parts) > 3 else "غير محدد"
                file_size = report_file.stat().st_size / 1024  # KB
                
                print(f"  {i}. {project_name} ({timestamp}) - {file_size:.1f} KB")
            else:
                print(f"  {i}. {report_file.name}")
        
        if len(report_files) > 10:
            print(f"  ... و {len(report_files) - 10} تقرير آخر")
    
    def show_system_settings(self):
        """عرض إعدادات النظام"""
        print("\n🔧 إعدادات نظام HORUS (منفصل)")
        print("=" * 40)
        
        print("⚙️ الإعدادات الحالية:")
        print("  • نوع النظام: منفصل ومستقل")
        print("  • عمق التحليل: شامل")
        print("  • تنسيق التقرير: JSON")
        print("  • الحفظ التلقائي: مفعل")
        print("  • الذاكرة المتقدمة: مفعل")
        print(f"  • موقع النظام: {self.horus_root}")
        
        print("\n🛠️ خيارات الصيانة:")
        print("1. 🧹 تنظيف الذاكرة")
        print("2. 💾 نسخ احتياطي")
        print("3. 📊 تصدير البيانات")
        print("0. 🔙 رجوع")
        
        choice = input("\n🎯 اختيارك: ").strip()
        
        if choice == "1":
            print("🧹 تنظيف الذاكرة...")
            print("✅ تم تنظيف الذاكرة بنجاح")
        elif choice == "2":
            print("💾 إنشاء نسخة احتياطية...")
            print("✅ تم إنشاء النسخة الاحتياطية")
        elif choice == "3":
            print("📊 تصدير البيانات...")
            print("✅ تم تصدير البيانات")
    
    def show_about_horus(self):
        """عرض معلومات حول HORUS"""
        print("\n" + "🦅" * 20)
        print("ℹ️  معلومات حول نظام HORUS المنفصل")
        print("🦅" * 20)
        
        print(f"""
🦅 HORUS - محلل المشاريع المتقدم
Advanced Project Analyzer System (Independent)

📋 الوصف:
نظام متقدم ومنفصل لتحليل المشاريع البرمجية بجميع أنواعها
وتقديم تقارير شاملة وتوصيات ذكية.

🌟 المميزات:
• نظام منفصل ومستقل تماماً
• تحليل شامل لأي نوع مشروع
• ذاكرة متقدمة للتعلم والتطور
• تقارير احترافية متعددة التنسيقات
• واجهات متنوعة سهلة الاستخدام

📊 الإحصائيات:
• محركات تحليل متخصصة
• دعم 15+ لغة برمجة
• واجهات متعددة
• ذاكرة ذكية متقدمة

📍 معلومات النظام:
• الموقع: {self.horus_root}
• الحالة: نظام منفصل ومستقل
• منفصل عن: standalone-ai-assistants
• تاريخ الفصل: 2025-07-07

👤 المطور: Augment Agent
📅 تاريخ الإنشاء: 2025-07-07
📝 الإصدار: 1.0.0 (Independent)
🛡️ الحالة: نظام منفصل ومتقدم
        """)
    
    def show_analysis_summary(self, results):
        """عرض ملخص التحليل"""
        print("\n" + "🦅" * 20)
        print("📊 ملخص التحليل (نظام منفصل)")
        print("🦅" * 20)
        
        metadata = results.get("metadata", {})
        structure = results.get("structure", {})
        quality = results.get("quality", {})
        recommendations = results.get("recommendations", [])
        
        print(f"🆔 معرف التحليل: {metadata.get('analysis_id', 'غير محدد')}")
        print(f"📁 اسم المشروع: {metadata.get('project_name', 'غير محدد')}")
        print(f"🏠 موقع HORUS: {metadata.get('horus_location', 'غير محدد')}")
        print(f"🛡️ حالة النظام: {metadata.get('system_status', 'INDEPENDENT')}")
        
        project_types = structure.get('project_types', [])
        if project_types:
            types_str = ", ".join([f"{pt.get('type', 'غير محدد')} ({pt.get('confidence', 0)}%)" for pt in project_types[:3]])
            print(f"🏷️ نوع المشروع: {types_str}")
        
        file_stats = structure.get('file_statistics', {})
        print(f"📄 إجمالي الملفات: {file_stats.get('total_files', 0):,}")
        print(f"📂 إجمالي المجلدات: {file_stats.get('total_directories', 0):,}")
        
        quality_metrics = quality.get('quality_metrics', {})
        print(f"📊 نقاط الجودة: {quality_metrics.get('overall_quality_score', 0)}/100")
        print(f"🏆 تقييم الجودة: {quality_metrics.get('quality_grade', 'غير محدد')}")
        print(f"💡 عدد التوصيات: {len(recommendations)}")
        
        # عرض أهم التوصيات
        high_priority = [r for r in recommendations if r.get('priority') == 'high']
        if high_priority:
            print(f"\n🔴 أهم التوصيات:")
            for i, rec in enumerate(high_priority[:3], 1):
                print(f"  {i}. {rec.get('title', 'غير محدد')}")
    
    def run(self):
        """تشغيل الواجهة الرئيسية"""
        while True:
            self.show_welcome()
            
            try:
                choice = input("\n🎯 اختر رقم الخيار: ").strip()
                
                if choice == "1":
                    self.analyze_new_project()
                elif choice == "2":
                    self.show_last_analysis()
                elif choice == "3":
                    self.show_analysis_history()
                elif choice == "4":
                    self.show_memory_statistics()
                elif choice == "5":
                    self.show_saved_reports()
                elif choice == "6":
                    self.show_system_settings()
                elif choice == "7":
                    self.show_about_horus()
                elif choice == "0":
                    print("\n🦅 شكراً لاستخدام نظام HORUS المنفصل!")
                    print("🦅 نراك قريباً في تحليلات أفضل!")
                    break
                else:
                    print("❌ اختيار غير صحيح")
                
                input("\n⏸️ اضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n🦅 تم إيقاف نظام HORUS بواسطة المستخدم")
                break
            except Exception as e:
                print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    interface = HorusMainInterface()
    interface.run()

if __name__ == "__main__":
    main()
