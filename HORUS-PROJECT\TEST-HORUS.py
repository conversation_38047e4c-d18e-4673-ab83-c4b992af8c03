#!/usr/bin/env python3
"""
🦅 HORUS - اختبار النظام المنفصل
Test for HORUS Independent System
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

def test_independent_horus():
    """اختبار نظام HORUS المنفصل"""
    print("🦅" * 30)
    print("🦅 HORUS - اختبار النظام المنفصل 🦅")
    print("🦅" * 30)
    
    current_dir = Path(".").resolve()
    print(f"📍 موقع النظام: {current_dir}")
    print(f"📁 اسم المجلد: {current_dir.name}")
    
    # فحص الهيكل
    print("\n🔍 فحص هيكل النظام المنفصل...")
    
    required_dirs = [
        "MEMORY", "ANALYZERS", "REPORTS", "INTERFACES"
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not Path(dir_name).exists():
            missing_dirs.append(dir_name)
        else:
            print(f"  ✅ {dir_name}/")
    
    if missing_dirs:
        print(f"  ❌ مجلدات مفقودة: {', '.join(missing_dirs)}")
    else:
        print("  ✅ جميع المجلدات الأساسية موجودة")
    
    # فحص الملفات الأساسية
    print("\n📄 فحص الملفات الأساسية...")
    
    required_files = [
        "README.md",
        "START-HORUS.bat",
        "MEMORY/projects_database.json",
        "ANALYZERS/project_analyzer.py",
        "INTERFACES/main_interface.py"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
        else:
            print(f"  ✅ {file_name}")
    
    if missing_files:
        print(f"  ❌ ملفات مفقودة: {', '.join(missing_files)}")
    else:
        print("  ✅ جميع الملفات الأساسية موجودة")
    
    # اختبار الذاكرة
    print("\n🧠 اختبار الذاكرة المنفصلة...")
    
    try:
        memory_file = Path("MEMORY/projects_database.json")
        if memory_file.exists():
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            system_info = memory_data.get('system_info', {})
            horus_memory = memory_data.get('horus_memory', {})
            
            print(f"  ✅ الذاكرة تعمل")
            print(f"  🏠 موقع النظام: {system_info.get('project_location', 'غير محدد')}")
            print(f"  🛡️ حالة النظام: {system_info.get('independent_system', False)}")
            print(f"  📅 تاريخ الفصل: {system_info.get('separation_date', 'غير محدد')[:16]}")
            print(f"  📊 المشاريع المحللة: {horus_memory.get('total_projects_analyzed', 0)}")
        else:
            print("  ❌ ملف الذاكرة غير موجود")
    
    except Exception as e:
        print(f"  ❌ خطأ في الذاكرة: {e}")
    
    # اختبار المحلل
    print("\n🔍 اختبار المحلل...")
    
    try:
        sys.path.append(str(Path(".").resolve()))
        from ANALYZERS.project_analyzer import HorusProjectAnalyzer
        
        # إنشاء محلل تجريبي
        test_analyzer = HorusProjectAnalyzer(".")
        
        print(f"  ✅ المحلل يعمل")
        print(f"  📁 المشروع المختبر: {test_analyzer.project_name}")
        print(f"  🏠 موقع HORUS: {test_analyzer.horus_root}")
        print(f"  🆔 معرف التحليل: {test_analyzer.analysis_id}")
        
    except Exception as e:
        print(f"  ❌ خطأ في المحلل: {e}")
    
    # اختبار الواجهة
    print("\n🎯 اختبار الواجهة...")
    
    try:
        from INTERFACES.main_interface import HorusMainInterface
        
        # إنشاء واجهة تجريبية
        test_interface = HorusMainInterface()
        
        print(f"  ✅ الواجهة تعمل")
        print(f"  🏠 موقع النظام: {test_interface.horus_root}")
        
    except Exception as e:
        print(f"  ❌ خطأ في الواجهة: {e}")
    
    # إنشاء تقرير الاختبار
    print("\n📄 إنشاء تقرير الاختبار...")
    
    test_report = {
        "test_timestamp": datetime.now().isoformat(),
        "system_type": "INDEPENDENT_HORUS",
        "system_location": str(current_dir),
        "test_results": {
            "directories_check": len(missing_dirs) == 0,
            "files_check": len(missing_files) == 0,
            "memory_check": memory_file.exists() if 'memory_file' in locals() else False,
            "analyzer_check": 'test_analyzer' in locals(),
            "interface_check": 'test_interface' in locals()
        },
        "missing_components": {
            "directories": missing_dirs,
            "files": missing_files
        },
        "system_info": {
            "independent": True,
            "separated_from": "standalone-ai-assistants",
            "location": str(current_dir)
        }
    }
    
    # تحديد حالة النظام
    all_checks = all(test_report["test_results"].values())
    test_report["status"] = "PASS" if all_checks else "FAIL"
    
    # حفظ تقرير الاختبار
    reports_dir = Path("REPORTS")
    reports_dir.mkdir(exist_ok=True)
    
    test_report_file = reports_dir / f"independent_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(test_report_file, 'w', encoding='utf-8') as f:
        json.dump(test_report, f, ensure_ascii=False, indent=2)
    
    print(f"  ✅ تقرير الاختبار محفوظ: {test_report_file}")
    
    # النتيجة النهائية
    print(f"\n🦅 نتيجة اختبار النظام المنفصل: {test_report['status']}")
    
    if test_report['status'] == "PASS":
        print("🎉 نظام HORUS المنفصل جاهز للاستخدام!")
        print("🚀 يمكنك تشغيله بالأمر: START-HORUS.bat")
        print("📍 النظام منفصل تماماً ومستقل")
    else:
        print("⚠️ نظام HORUS المنفصل يحتاج إصلاحات")
        print("🔧 راجع المكونات المفقودة أعلاه")
    
    return test_report

def run_quick_analysis():
    """تشغيل تحليل سريع للنظام نفسه"""
    print("\n🔍 تحليل سريع للنظام نفسه...")
    
    try:
        from ANALYZERS.project_analyzer import HorusProjectAnalyzer
        
        # تحليل النظام نفسه
        analyzer = HorusProjectAnalyzer(".")
        print("🚀 بدء التحليل السريع...")
        
        # تحليل مبسط
        structure_results = analyzer.analyze_structure()
        
        print(f"✅ تم التحليل!")
        print(f"📁 اسم النظام: {structure_results['project_name']}")
        
        project_types = structure_results.get('project_types', [])
        if project_types:
            types_str = ", ".join([pt.get('type', 'غير محدد') for pt in project_types[:3]])
            print(f"🏷️ نوع النظام: {types_str}")
        
        file_stats = structure_results.get('file_statistics', {})
        print(f"📄 ملفات النظام: {file_stats.get('total_files', 0)}")
        print(f"📂 مجلدات النظام: {file_stats.get('total_directories', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحليل السريع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    # اختبار النظام
    test_result = test_independent_horus()
    
    # تحليل سريع إذا نجح الاختبار
    if test_result['status'] == "PASS":
        analysis_success = run_quick_analysis()
        
        if analysis_success:
            print("\n🎉 النظام المنفصل يعمل بشكل مثالي!")
        else:
            print("\n⚠️ النظام يعمل لكن التحليل يحتاج تحسين")
    
    print("\n🦅" * 30)
    print("🦅 انتهى اختبار النظام المنفصل 🦅")
    print("🦅" * 30)

if __name__ == "__main__":
    main()
