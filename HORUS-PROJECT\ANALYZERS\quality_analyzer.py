#!/usr/bin/env python3
"""
📊 HORUS - محلل جودة الكود
Quality Analyzer for HORUS System
"""

import re
from pathlib import Path
from collections import defaultdict

class QualityAnalyzer:
    """محلل جودة الكود"""
    
    def __init__(self, project_path):
        self.project_path = Path(project_path)
        
        # أنماط ملفات التوثيق
        self.documentation_patterns = [
            "readme", "changelog", "license", "contributing", "authors",
            "install", "usage", "api", "guide", "tutorial", "docs"
        ]
        
        # أنماط ملفات الاختبار
        self.test_patterns = [
            "test", "spec", "__test__", "tests", "testing", "unittest"
        ]
        
        # أنماط ملفات التكوين
        self.config_patterns = [
            ".json", ".yml", ".yaml", ".toml", ".ini", ".cfg", ".env",
            ".config", ".conf", ".properties"
        ]
        
        # امتدادات ملفات الكود
        self.code_extensions = [
            ".py", ".js", ".ts", ".jsx", ".tsx", ".java", ".cs", ".php",
            ".rb", ".go", ".rs", ".cpp", ".c", ".h", ".hpp", ".swift",
            ".kt", ".scala", ".clj", ".hs", ".ml", ".r", ".m"
        ]
    
    def analyze(self):
        """تحليل جودة الكود"""
        results = {
            "file_categories": self.categorize_files(),
            "documentation_analysis": self.analyze_documentation(),
            "test_coverage_estimate": self.estimate_test_coverage(),
            "code_organization": self.analyze_code_organization(),
            "naming_conventions": self.analyze_naming_conventions(),
            "complexity_indicators": self.analyze_complexity_indicators(),
            "quality_metrics": {}
        }
        
        # حساب المقاييس النهائية
        results["quality_metrics"] = self.calculate_quality_metrics(results)
        
        return results
    
    def categorize_files(self):
        """تصنيف الملفات"""
        categories = {
            "documentation_files": 0,
            "test_files": 0,
            "config_files": 0,
            "code_files": 0,
            "asset_files": 0,
            "other_files": 0
        }
        
        file_details = {
            "documentation": [],
            "tests": [],
            "config": [],
            "code": [],
            "assets": [],
            "other": []
        }
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                file_name = file_path.name.lower()
                file_ext = file_path.suffix.lower()
                relative_path = str(file_path.relative_to(self.project_path))
                
                # تصنيف الملف
                if self.is_documentation_file(file_name, file_ext):
                    categories["documentation_files"] += 1
                    file_details["documentation"].append(relative_path)
                elif self.is_test_file(file_name, file_ext, relative_path):
                    categories["test_files"] += 1
                    file_details["tests"].append(relative_path)
                elif self.is_config_file(file_name, file_ext):
                    categories["config_files"] += 1
                    file_details["config"].append(relative_path)
                elif self.is_code_file(file_ext):
                    categories["code_files"] += 1
                    file_details["code"].append(relative_path)
                elif self.is_asset_file(file_ext):
                    categories["asset_files"] += 1
                    file_details["assets"].append(relative_path)
                else:
                    categories["other_files"] += 1
                    file_details["other"].append(relative_path)
        
        return {
            "counts": categories,
            "details": file_details
        }
    
    def analyze_documentation(self):
        """تحليل التوثيق"""
        doc_analysis = {
            "has_readme": False,
            "has_changelog": False,
            "has_license": False,
            "has_contributing": False,
            "documentation_score": 0,
            "documentation_files": []
        }
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                file_name = file_path.name.lower()
                
                if "readme" in file_name:
                    doc_analysis["has_readme"] = True
                    doc_analysis["documentation_score"] += 3
                
                if "changelog" in file_name or "history" in file_name:
                    doc_analysis["has_changelog"] = True
                    doc_analysis["documentation_score"] += 2
                
                if "license" in file_name:
                    doc_analysis["has_license"] = True
                    doc_analysis["documentation_score"] += 2
                
                if "contributing" in file_name:
                    doc_analysis["has_contributing"] = True
                    doc_analysis["documentation_score"] += 1
                
                if self.is_documentation_file(file_name, file_path.suffix.lower()):
                    relative_path = str(file_path.relative_to(self.project_path))
                    doc_analysis["documentation_files"].append(relative_path)
        
        # تقييم جودة التوثيق
        max_score = 8
        doc_analysis["documentation_quality"] = min(doc_analysis["documentation_score"] / max_score * 100, 100)
        
        return doc_analysis
    
    def estimate_test_coverage(self):
        """تقدير تغطية الاختبارات"""
        test_files = 0
        code_files = 0
        test_lines = 0
        code_lines = 0
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                file_name = file_path.name.lower()
                file_ext = file_path.suffix.lower()
                relative_path = str(file_path.relative_to(self.project_path))
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = len(f.readlines())
                    
                    if self.is_test_file(file_name, file_ext, relative_path):
                        test_files += 1
                        test_lines += lines
                    elif self.is_code_file(file_ext):
                        code_files += 1
                        code_lines += lines
                
                except Exception:
                    pass
        
        # حساب النسب
        test_to_code_ratio = (test_files / code_files * 100) if code_files > 0 else 0
        test_lines_ratio = (test_lines / code_lines * 100) if code_lines > 0 else 0
        
        return {
            "test_files": test_files,
            "code_files": code_files,
            "test_lines": test_lines,
            "code_lines": code_lines,
            "test_to_code_file_ratio": round(test_to_code_ratio, 2),
            "test_to_code_lines_ratio": round(test_lines_ratio, 2),
            "estimated_coverage": min(test_lines_ratio * 2, 100)  # تقدير تقريبي
        }
    
    def analyze_code_organization(self):
        """تحليل تنظيم الكود"""
        organization = {
            "directory_depth": 0,
            "files_per_directory": {},
            "large_files": [],
            "organization_score": 0
        }
        
        max_depth = 0
        dir_file_counts = defaultdict(int)
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file():
                # حساب العمق
                relative_path = file_path.relative_to(self.project_path)
                depth = len(relative_path.parts) - 1
                max_depth = max(max_depth, depth)
                
                # عد الملفات في كل مجلد
                parent_dir = str(relative_path.parent) if relative_path.parent != Path('.') else 'root'
                dir_file_counts[parent_dir] += 1
                
                # البحث عن الملفات الكبيرة
                try:
                    if self.is_code_file(file_path.suffix.lower()):
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = len(f.readlines())
                        
                        if lines > 500:  # ملفات كبيرة
                            organization["large_files"].append({
                                "path": str(relative_path),
                                "lines": lines
                            })
                except Exception:
                    pass
        
        organization["directory_depth"] = max_depth
        organization["files_per_directory"] = dict(dir_file_counts)
        
        # حساب نقاط التنظيم
        score = 10
        if max_depth > 8:
            score -= 3
        elif max_depth > 5:
            score -= 1
        
        if len(organization["large_files"]) > 10:
            score -= 2
        elif len(organization["large_files"]) > 5:
            score -= 1
        
        organization["organization_score"] = max(score, 0)
        
        return organization
    
    def analyze_naming_conventions(self):
        """تحليل اصطلاحات التسمية"""
        naming = {
            "file_naming_patterns": {},
            "directory_naming_patterns": {},
            "consistency_score": 0
        }
        
        file_patterns = defaultdict(int)
        dir_patterns = defaultdict(int)
        
        for path in self.project_path.rglob("*"):
            name = path.name
            
            if path.is_file():
                # تحليل أنماط تسمية الملفات
                if '_' in name:
                    file_patterns["snake_case"] += 1
                elif '-' in name:
                    file_patterns["kebab_case"] += 1
                elif any(c.isupper() for c in name):
                    file_patterns["camelCase"] += 1
                else:
                    file_patterns["lowercase"] += 1
            
            elif path.is_dir():
                # تحليل أنماط تسمية المجلدات
                if '_' in name:
                    dir_patterns["snake_case"] += 1
                elif '-' in name:
                    dir_patterns["kebab_case"] += 1
                elif any(c.isupper() for c in name):
                    dir_patterns["camelCase"] += 1
                else:
                    dir_patterns["lowercase"] += 1
        
        naming["file_naming_patterns"] = dict(file_patterns)
        naming["directory_naming_patterns"] = dict(dir_patterns)
        
        # حساب نقاط الاتساق
        total_files = sum(file_patterns.values())
        total_dirs = sum(dir_patterns.values())
        
        if total_files > 0:
            most_common_file_pattern = max(file_patterns.values())
            file_consistency = most_common_file_pattern / total_files * 100
        else:
            file_consistency = 100
        
        if total_dirs > 0:
            most_common_dir_pattern = max(dir_patterns.values())
            dir_consistency = most_common_dir_pattern / total_dirs * 100
        else:
            dir_consistency = 100
        
        naming["consistency_score"] = (file_consistency + dir_consistency) / 2
        
        return naming
    
    def analyze_complexity_indicators(self):
        """تحليل مؤشرات التعقيد"""
        complexity = {
            "average_file_size": 0,
            "max_file_size": 0,
            "deeply_nested_files": 0,
            "complexity_score": 0
        }
        
        file_sizes = []
        max_size = 0
        deeply_nested = 0
        
        for file_path in self.project_path.rglob("*"):
            if file_path.is_file() and self.is_code_file(file_path.suffix.lower()):
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = len(f.readlines())
                    
                    file_sizes.append(lines)
                    max_size = max(max_size, lines)
                    
                    if lines > 1000:
                        deeply_nested += 1
                
                except Exception:
                    pass
        
        if file_sizes:
            complexity["average_file_size"] = sum(file_sizes) / len(file_sizes)
        
        complexity["max_file_size"] = max_size
        complexity["deeply_nested_files"] = deeply_nested
        
        # حساب نقاط التعقيد
        score = 10
        if complexity["average_file_size"] > 500:
            score -= 3
        elif complexity["average_file_size"] > 200:
            score -= 1
        
        if deeply_nested > 5:
            score -= 2
        elif deeply_nested > 0:
            score -= 1
        
        complexity["complexity_score"] = max(score, 0)
        
        return complexity
    
    def calculate_quality_metrics(self, results):
        """حساب المقاييس النهائية للجودة"""
        file_cats = results["file_categories"]["counts"]
        doc_analysis = results["documentation_analysis"]
        test_coverage = results["test_coverage_estimate"]
        organization = results["code_organization"]
        naming = results["naming_conventions"]
        complexity = results["complexity_indicators"]
        
        total_files = sum(file_cats.values())
        
        # حساب النسب
        doc_ratio = (file_cats["documentation_files"] / total_files * 100) if total_files > 0 else 0
        test_ratio = (file_cats["test_files"] / total_files * 100) if total_files > 0 else 0
        
        # النقاط الإجمالية
        total_score = (
            doc_analysis["documentation_quality"] * 0.25 +
            test_coverage["estimated_coverage"] * 0.30 +
            organization["organization_score"] * 10 * 0.20 +
            naming["consistency_score"] * 0.15 +
            complexity["complexity_score"] * 10 * 0.10
        )
        
        return {
            "documentation_files": file_cats["documentation_files"],
            "test_files": file_cats["test_files"],
            "code_files": file_cats["code_files"],
            "total_files": total_files,
            "doc_ratio": f"{doc_ratio:.1f}%",
            "test_ratio": f"{test_ratio:.1f}%",
            "doc_ratio_numeric": round(doc_ratio, 1),
            "test_ratio_numeric": round(test_ratio, 1),
            "overall_quality_score": round(total_score, 1),
            "quality_grade": self.get_quality_grade(total_score)
        }
    
    def get_quality_grade(self, score):
        """تحديد درجة الجودة"""
        if score >= 90:
            return "ممتاز"
        elif score >= 80:
            return "جيد جداً"
        elif score >= 70:
            return "جيد"
        elif score >= 60:
            return "مقبول"
        else:
            return "يحتاج تحسين"
    
    # دوال مساعدة
    def is_documentation_file(self, file_name, file_ext):
        """فحص ملف التوثيق"""
        return (any(pattern in file_name for pattern in self.documentation_patterns) or
                file_ext in [".md", ".rst", ".txt", ".adoc"])
    
    def is_test_file(self, file_name, file_ext, file_path):
        """فحص ملف الاختبار"""
        return (any(pattern in file_name for pattern in self.test_patterns) or
                any(pattern in file_path.lower() for pattern in self.test_patterns))
    
    def is_config_file(self, file_name, file_ext):
        """فحص ملف التكوين"""
        return file_ext in self.config_patterns
    
    def is_code_file(self, file_ext):
        """فحص ملف الكود"""
        return file_ext in self.code_extensions
    
    def is_asset_file(self, file_ext):
        """فحص ملف الأصول"""
        asset_extensions = [
            ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico",
            ".css", ".scss", ".sass", ".less",
            ".mp4", ".avi", ".mov", ".mp3", ".wav",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx"
        ]
        return file_ext in asset_extensions
