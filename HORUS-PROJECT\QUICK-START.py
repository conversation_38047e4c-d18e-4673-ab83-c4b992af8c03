#!/usr/bin/env python3
"""
🦅 HORUS - تشغيل سريع للنظام المنفصل
Quick Start for HORUS Independent System
"""

import os
import sys
from pathlib import Path

def main():
    """تشغيل سريع لنظام HORUS"""
    print("🦅" * 30)
    print("🦅 HORUS - نظام منفصل لتحليل المشاريع 🦅")
    print("🦅" * 30)
    
    current_dir = Path(".").resolve()
    print(f"📍 موقع النظام: {current_dir}")
    print(f"🛡️ حالة النظام: منفصل ومستقل")
    
    print("\n🎯 الخيارات المتاحة:")
    print("1. 🔍 تحليل مشروع")
    print("2. 📊 عرض معلومات النظام")
    print("3. 🧠 عرض الذاكرة")
    print("4. 🚀 تشغيل الواجهة الكاملة")
    print("0. 🚪 خروج")
    
    while True:
        try:
            choice = input("\n🎯 اختيارك: ").strip()
            
            if choice == "1":
                analyze_project()
            elif choice == "2":
                show_system_info()
            elif choice == "3":
                show_memory_info()
            elif choice == "4":
                launch_full_interface()
            elif choice == "0":
                print("\n🦅 شكراً لاستخدام نظام HORUS المنفصل!")
                break
            else:
                print("❌ اختيار غير صحيح")
        
        except KeyboardInterrupt:
            print("\n\n🦅 تم إيقاف النظام")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

def analyze_project():
    """تحليل مشروع"""
    print("\n🔍 تحليل مشروع")
    print("-" * 20)
    
    project_path = input("📁 أدخل مسار المشروع (أو اتركه فارغ للمشروع الحالي): ").strip()
    
    if not project_path:
        project_path = "."
    
    try:
        sys.path.append(str(Path(".").resolve()))
        from ANALYZERS.project_analyzer import HorusProjectAnalyzer
        
        print(f"\n🚀 بدء تحليل: {project_path}")
        analyzer = HorusProjectAnalyzer(project_path)
        results = analyzer.run_full_analysis()
        
        if results:
            print("\n✅ تم التحليل بنجاح!")
        else:
            print("\n❌ فشل في التحليل")
    
    except Exception as e:
        print(f"❌ خطأ في التحليل: {e}")

def show_system_info():
    """عرض معلومات النظام"""
    print("\n📊 معلومات نظام HORUS المنفصل")
    print("-" * 40)
    
    current_dir = Path(".").resolve()
    
    print(f"🏠 موقع النظام: {current_dir}")
    print(f"📁 اسم المجلد: {current_dir.name}")
    print(f"🛡️ حالة النظام: منفصل ومستقل")
    print(f"📅 تاريخ الإنشاء: 2025-07-07")
    print(f"📝 الإصدار: 1.0.0 (Independent)")
    
    # فحص المكونات
    components = [
        ("MEMORY", "ذاكرة النظام"),
        ("ANALYZERS", "محركات التحليل"),
        ("INTERFACES", "واجهات المستخدم"),
        ("REPORTS", "التقارير"),
        ("TOOLS", "الأدوات المساعدة")
    ]
    
    print(f"\n🔧 المكونات:")
    for folder, description in components:
        status = "✅" if Path(folder).exists() else "❌"
        print(f"  {status} {folder}/ - {description}")

def show_memory_info():
    """عرض معلومات الذاكرة"""
    print("\n🧠 ذاكرة نظام HORUS")
    print("-" * 30)
    
    try:
        import json
        memory_file = Path("MEMORY/projects_database.json")
        
        if memory_file.exists():
            with open(memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
            
            horus_memory = memory_data.get("horus_memory", {})
            system_info = memory_data.get("system_info", {})
            projects = memory_data.get("projects", {})
            
            print(f"📊 المشاريع المحللة: {horus_memory.get('total_projects_analyzed', 0)}")
            print(f"💾 حجم الذاكرة: {horus_memory.get('memory_size_mb', 0)} MB")
            print(f"🔄 آخر تحديث: {horus_memory.get('last_updated', 'غير محدد')[:16]}")
            print(f"🏠 موقع النظام: {system_info.get('project_location', 'غير محدد')}")
            print(f"🛡️ نظام مستقل: {system_info.get('independent_system', True)}")
            
            if projects:
                print(f"\n📁 آخر المشاريع المحللة:")
                for i, (key, project) in enumerate(list(projects.items())[-3:], 1):
                    name = project.get("name", "غير محدد")
                    last_analyzed = project.get("last_analyzed", "غير محدد")[:16]
                    print(f"  {i}. {name} - {last_analyzed}")
        else:
            print("❌ ملف الذاكرة غير موجود")
    
    except Exception as e:
        print(f"❌ خطأ في قراءة الذاكرة: {e}")

def launch_full_interface():
    """تشغيل الواجهة الكاملة"""
    print("\n🚀 تشغيل الواجهة الكاملة...")
    
    try:
        sys.path.append(str(Path(".").resolve()))
        from INTERFACES.main_interface import HorusMainInterface
        
        interface = HorusMainInterface()
        interface.run()
    
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة: {e}")

if __name__ == "__main__":
    main()
